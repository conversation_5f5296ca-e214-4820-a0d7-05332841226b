# Prompt - AI文案改写助手

一个功能强大的浏览器侧边栏扩展，通过AI大模型API快速改写文案内容。

## 功能特性

### 🎯 核心功能
- **侧边栏界面**: 右侧边栏形式，不干扰正常浏览
- **分类管理**: 自定义提示词分类，便于组织管理
- **AI改写**: 集成Gemini 2.5 Flash等AI模型，快速改写文案
- **本地存储**: 所有数据本地存储，保护隐私安全

### 📋 主要特性
- **提示词管理**: 添加、编辑、删除自定义提示词
- **分类系统**: 支持自定义分类标签
- **AI模型配置**: 支持多种AI模型，可自定义API配置
- **一键改写**: 选择提示词，输入原文，一键获得改写结果
- **结果复制**: 改写结果一键复制到剪贴板

## 安装方法

### 开发者模式安装

1. **下载源码**
   ```bash
   git clone <repository-url>
   cd prompt-extension
   ```

2. **打开Chrome扩展管理页面**
   - 在Chrome地址栏输入: `chrome://extensions/`
   - 或者通过菜单: 更多工具 → 扩展程序

3. **启用开发者模式**
   - 点击右上角的"开发者模式"开关

4. **加载扩展**
   - 点击"加载已解压的扩展程序"
   - 选择项目文件夹

5. **启用侧边栏**
   - 点击扩展图标或右键选择"打开侧边栏"

## 使用指南

### 首次设置

1. **配置API**
   - 点击底部"设置"按钮
   - 输入你的API Key和端点URL
   - 默认配置了Gemini 2.5 Flash模型

2. **添加提示词**
   - 点击右上角"+"按钮
   - 填写提示词名称和内容
   - 选择分类和推荐模型
   - 点击保存

### 日常使用

1. **选择分类**
   - 点击顶部分类标签切换不同类别的提示词

2. **使用提示词**
   - 点击提示词卡片的"复制"按钮
   - 在弹窗中输入原始文案
   - 点击"开始改写"等待AI处理
   - 复制改写结果

3. **管理提示词**
   - 点击提示词卡片主体进入编辑模式
   - 可以修改名称、内容、分类等信息
   - 支持删除不需要的提示词

## 默认配置

### 预置提示词
- **论文大师提示词**: 学术写作专用
- **微信表情包提示词**: 社交媒体文案

### 默认分类
- 全部
- 创意设计
- 内容可视化
- 学习提升

### AI模型配置
- **模型**: Gemini 2.5 Flash
- **API端点**: https://generativelanguage.googleapis.com/v1beta/chat/completions
- **API Key**: 需要用户自行配置

## 技术架构

### 文件结构
```
prompt-extension/
├── manifest.json          # 扩展配置
├── background.js           # 后台脚本
├── sidepanel.html         # 主界面
├── sidepanel.css          # 样式文件
├── sidepanel.js           # 主逻辑
├── components/            # 组件模块
│   ├── api.js            # AI API调用
│   ├── modal.js          # 弹窗管理
│   └── modal.css         # 弹窗样式
├── icons/                # 图标文件
└── README.md             # 说明文档
```

### 技术栈
- **框架**: Manifest V3
- **前端**: HTML5 + CSS3 + JavaScript ES6+
- **存储**: Chrome Storage API
- **AI集成**: Fetch API + Gemini API

## 开发说明

### 本地开发
1. 修改代码后，在扩展管理页面点击刷新按钮
2. 侧边栏会自动重新加载最新代码

### 调试方法
1. 右键侧边栏 → 检查元素，打开开发者工具
2. 查看Console面板的日志信息
3. 使用Network面板调试API调用

### 自定义配置
- 修改 `background.js` 中的默认数据
- 在 `components/api.js` 中添加新的AI模型支持
- 通过CSS自定义界面样式

## 常见问题

### Q: API调用失败怎么办？
A: 检查API Key是否正确，网络连接是否正常，API端点URL是否有效。

### Q: 如何添加新的AI模型？
A: 在设置中配置新的模型信息，或修改代码中的模型配置。

### Q: 数据会丢失吗？
A: 所有数据存储在本地，不会上传到服务器，但建议定期备份重要的提示词。

### Q: 支持导入导出吗？
A: 当前版本暂不支持，后续版本会添加此功能。

## 更新日志

### v1.0.0 (2024-12-XX)
- 初始版本发布
- 基础的提示词管理功能
- Gemini API集成
- 侧边栏界面实现

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- GitHub Issues
- Email: [<EMAIL>]
