# 🚀 Prompt 扩展快速启动指南

## 立即安装使用

### 1. 加载扩展到 Chrome
1. 打开 Chrome 浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 开启右上角的"开发者模式"开关
4. 点击"加载已解压的扩展程序"
5. 选择项目文件夹 `d:\ai\prompt`
6. 扩展安装成功！

### 2. 启用侧边栏
1. 点击浏览器工具栏中的扩展图标（可能显示为拼图图标）
2. 找到 "Prompt - AI文案改写助手"
3. 点击扩展图标或右键选择"打开侧边栏"
4. 侧边栏将在浏览器右侧打开

### 3. 首次配置
1. 点击侧边栏底部的"设置"按钮
2. 输入您的 Gemini API Key：`AIzaSyDJ8RG1hMXCNWNlQ-uCzeCQCRq_RRx28Bc`
3. 确认 API 端点 URL：`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent`
4. 点击"保存设置"

### 4. 开始使用
1. 扩展已预置了两个示例提示词：
   - 论文大师提示词（学习提升类）
   - 微信表情包提示词（创意设计类）
2. 点击提示词卡片的"使用"按钮
3. 输入您要改写的文案
4. 点击"开始改写"
5. 复制改写结果

## 🔧 故障排除

### 如果扩展无法加载
- ✅ 已解决：图标文件问题已修复
- 确保所有文件都在 `d:\ai\prompt` 文件夹中
- 检查是否开启了开发者模式

### 如果侧边栏无法打开
- 确认扩展已正确安装
- 尝试刷新页面后重新打开
- 检查浏览器版本是否支持 Side Panel API（Chrome 114+）

### 如果 API 调用失败
- 验证 API Key 是否正确
- 检查网络连接
- 确认 API 端点 URL 正确

## 📋 预置内容

### 默认提示词
1. **论文大师提示词**
   - 分类：学习提升
   - 功能：将文本改写为学术化表达

2. **微信表情包提示词**
   - 分类：创意设计
   - 功能：转换为有趣的表情包文案

### 默认分类
- 全部
- 创意设计
- 内容可视化
- 学习提升

## 🎯 主要功能

- ✅ 提示词管理（添加、编辑、删除）
- ✅ 分类系统
- ✅ AI 改写功能
- ✅ 数据导入导出
- ✅ 设置配置
- ✅ 本地数据存储

## 📞 技术支持

如遇到问题：
1. 检查浏览器控制台的错误信息
2. 确认扩展权限已授予
3. 尝试重新加载扩展

---

**现在就可以开始使用了！** 🎉
