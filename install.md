# Prompt 扩展安装指南

## 快速安装步骤

### 1. 准备工作
确保您使用的是 Chrome 浏览器（版本 88 或更高）

### 2. 下载扩展
- 下载或克隆此项目到本地
- 确保所有文件都在同一个文件夹中

### 3. 生成图标文件
1. 在浏览器中打开 `icons/create-icons.html`
2. 点击按钮生成 16x16、48x48、128x128 三个尺寸的图标
3. 将生成的图标文件保存到 `icons/` 文件夹中
4. 确保文件名为：`icon16.png`、`icon48.png`、`icon128.png`

### 4. 加载扩展到 Chrome
1. 打开 Chrome 浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 开启右上角的"开发者模式"开关
4. 点击"加载已解压的扩展程序"
5. 选择项目文件夹
6. 扩展安装成功后会出现在扩展列表中

### 5. 启用侧边栏
1. 点击浏览器工具栏中的扩展图标
2. 或者右键点击扩展图标，选择"打开侧边栏"
3. 侧边栏将在浏览器右侧打开

### 6. 首次配置
1. 点击侧边栏底部的"设置"按钮
2. 输入您的 Gemini API Key
3. 确认 API 端点 URL 正确
4. 保存设置

## 默认配置信息

### API 配置
- **模型**: Gemini 2.5 Flash
- **API 端点**: `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent`
- **API Key**: 需要您自行申请和配置

### 预置提示词
扩展包含以下预置提示词：
- 论文大师提示词（学习提升类）
- 微信表情包提示词（创意设计类）

### 预置分类
- 全部
- 创意设计
- 内容可视化
- 学习提升

## 获取 Gemini API Key

1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 登录您的 Google 账户
3. 创建新的 API Key
4. 复制 API Key 并在扩展设置中配置

## 常见问题解决

### 扩展无法加载
- 检查所有文件是否完整
- 确保 manifest.json 格式正确
- 检查是否开启了开发者模式

### 侧边栏无法打开
- 确认扩展已正确安装
- 尝试刷新页面后重新打开
- 检查浏览器版本是否支持 Side Panel API

### API 调用失败
- 验证 API Key 是否正确
- 检查网络连接
- 确认 API 端点 URL 正确

### 数据丢失
- 数据存储在本地，清除浏览器数据会导致丢失
- 建议定期使用导出功能备份数据
- 重新安装扩展前请先导出数据

## 卸载扩展

1. 打开 `chrome://extensions/`
2. 找到 Prompt 扩展
3. 点击"移除"按钮
4. 确认卸载

注意：卸载扩展会清除所有本地数据，请提前备份重要的提示词。

## 技术支持

如遇到问题，请检查：
1. 浏览器控制台的错误信息
2. 扩展的后台页面日志
3. 网络请求是否正常

更多技术细节请参考 README.md 文件。
