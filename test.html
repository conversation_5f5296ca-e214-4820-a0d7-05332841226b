<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prompt 扩展测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background: #fafafa;
        }
        .test-section h2 {
            color: #6366f1;
            margin-top: 0;
        }
        .sample-text {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #6366f1;
            margin: 15px 0;
        }
        .instructions {
            background: #e0e7ff;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        .status.info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
        button {
            background: #6366f1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #5856eb;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e0e0e0;
        }
        .feature-list li:before {
            content: "✓";
            color: #10b981;
            font-weight: bold;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Prompt 扩展测试页面</h1>
        
        <div class="instructions">
            <h3>测试说明：</h3>
            <p>1. 确保已经在Chrome中加载了Prompt扩展</p>
            <p>2. 点击扩展图标或右键选择"打开侧边栏"</p>
            <p>3. 使用下方的示例文本测试改写功能</p>
            <p>4. 检查各项功能是否正常工作</p>
        </div>

        <div class="test-section">
            <h2>📝 示例文本 - 学术写作</h2>
            <div class="sample-text">
                <strong>原文：</strong><br>
                这个研究很重要，因为它能帮助我们更好地理解人工智能的发展趋势。我们通过分析大量数据，发现了一些有趣的规律。这些发现对未来的AI研究有很大的指导意义。
            </div>
            <p><strong>测试目标：</strong>使用"论文大师提示词"将上述文本改写为学术化表达</p>
        </div>

        <div class="test-section">
            <h2>💬 示例文本 - 社交媒体</h2>
            <div class="sample-text">
                <strong>原文：</strong><br>
                今天天气真好，阳光明媚，心情特别棒！决定出去走走，享受这美好的一天。生活就是要这样积极向上！
            </div>
            <p><strong>测试目标：</strong>使用"微信表情包提示词"将上述文本转换为有趣的表情包文案</p>
        </div>

        <div class="test-section">
            <h2>🔧 功能测试清单</h2>
            <ul class="feature-list">
                <li>侧边栏正常打开和显示</li>
                <li>分类标签切换功能</li>
                <li>添加新提示词功能</li>
                <li>编辑现有提示词功能</li>
                <li>AI改写功能正常工作</li>
                <li>复制改写结果功能</li>
                <li>设置页面配置API</li>
                <li>数据本地存储和读取</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>⚙️ 扩展状态检查</h2>
            <div id="extensionStatus">
                <div class="status info">正在检查扩展状态...</div>
            </div>
            <button onclick="checkExtensionStatus()">重新检查</button>
        </div>

        <div class="test-section">
            <h2>🐛 常见问题排查</h2>
            <h3>如果侧边栏无法打开：</h3>
            <ul>
                <li>检查扩展是否已正确加载</li>
                <li>确认扩展权限已授予</li>
                <li>尝试刷新扩展或重新加载</li>
            </ul>
            
            <h3>如果AI改写失败：</h3>
            <ul>
                <li>检查API Key是否正确配置</li>
                <li>确认网络连接正常</li>
                <li>查看浏览器控制台的错误信息</li>
            </ul>
            
            <h3>如果数据丢失：</h3>
            <ul>
                <li>检查Chrome存储权限</li>
                <li>确认没有清除浏览器数据</li>
                <li>尝试重新添加提示词</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>📊 性能测试</h2>
            <p>测试扩展在不同场景下的性能表现：</p>
            <button onclick="performanceTest()">运行性能测试</button>
            <div id="performanceResults"></div>
        </div>
    </div>

    <script>
        function checkExtensionStatus() {
            const statusDiv = document.getElementById('extensionStatus');
            
            // 检查是否在扩展环境中
            if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id) {
                statusDiv.innerHTML = '<div class="status success">✅ 扩展环境检测成功</div>';
            } else {
                statusDiv.innerHTML = '<div class="status error">❌ 未检测到扩展环境，请确保在Chrome中打开此页面</div>';
            }
            
            // 检查侧边栏API
            if (typeof chrome !== 'undefined' && chrome.sidePanel) {
                statusDiv.innerHTML += '<div class="status success">✅ 侧边栏API可用</div>';
            } else {
                statusDiv.innerHTML += '<div class="status error">❌ 侧边栏API不可用</div>';
            }
            
            // 检查存储API
            if (typeof chrome !== 'undefined' && chrome.storage) {
                statusDiv.innerHTML += '<div class="status success">✅ 存储API可用</div>';
            } else {
                statusDiv.innerHTML += '<div class="status error">❌ 存储API不可用</div>';
            }
        }

        function performanceTest() {
            const resultsDiv = document.getElementById('performanceResults');
            resultsDiv.innerHTML = '<div class="status info">正在运行性能测试...</div>';
            
            const startTime = performance.now();
            
            // 模拟一些操作
            setTimeout(() => {
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                resultsDiv.innerHTML = `
                    <div class="status success">
                        ✅ 性能测试完成<br>
                        页面加载时间: ${duration.toFixed(2)}ms<br>
                        内存使用: ${(performance.memory ? performance.memory.usedJSHeapSize / 1024 / 1024 : 'N/A')} MB
                    </div>
                `;
            }, 100);
        }

        // 页面加载时自动检查状态
        document.addEventListener('DOMContentLoaded', () => {
            checkExtensionStatus();
        });
    </script>
</body>
</html>
