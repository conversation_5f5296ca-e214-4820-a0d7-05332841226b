<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成扩展图标</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .icon-generator {
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 600px;
            margin: 0 auto;
        }
        .icon-preview {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .icon {
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            color: white;
            font-weight: bold;
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
        }
        .icon-16 { width: 16px; height: 16px; font-size: 8px; }
        .icon-48 { width: 48px; height: 48px; font-size: 24px; }
        .icon-128 { width: 128px; height: 128px; font-size: 64px; }
        button {
            background: #6366f1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #5856eb;
        }
        .instructions {
            background: #e0e7ff;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="icon-generator">
        <h1>Prompt 扩展图标生成器</h1>
        
        <div class="instructions">
            <h3>使用说明：</h3>
            <p>1. 点击下方按钮生成对应尺寸的图标</p>
            <p>2. 右键点击生成的图标，选择"图片另存为"</p>
            <p>3. 保存为对应的文件名：icon16.png, icon48.png, icon128.png</p>
            <p>4. 将图标文件放在 icons/ 文件夹中</p>
        </div>

        <div class="icon-preview">
            <div class="icon icon-16" id="icon16">P</div>
            <div class="icon icon-48" id="icon48">P</div>
            <div class="icon icon-128" id="icon128">P</div>
        </div>

        <div>
            <button onclick="generateIcon(16)">生成 16x16 图标</button>
            <button onclick="generateIcon(48)">生成 48x48 图标</button>
            <button onclick="generateIcon(128)">生成 128x128 图标</button>
        </div>

        <canvas id="canvas" style="display: none;"></canvas>
    </div>

    <script>
        function generateIcon(size) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            canvas.width = size;
            canvas.height = size;
            
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#6366f1');
            gradient.addColorStop(1, '#8b5cf6');
            
            // 绘制背景
            ctx.fillStyle = gradient;
            ctx.roundRect(0, 0, size, size, size * 0.2);
            ctx.fill();
            
            // 绘制文字
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size * 0.6}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('P', size / 2, size / 2);
            
            // 转换为图片并下载
            canvas.toBlob(function(blob) {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `icon${size}.png`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            });
        }

        // 添加 roundRect 方法支持（如果浏览器不支持）
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
    </script>
</body>
</html>
