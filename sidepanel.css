/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 400px;
    margin: 0 auto;
}

/* 分类导航栏 */
.category-nav {
    display: flex;
    align-items: center;
    padding: 16px;
    background: white;
    border-bottom: 1px solid #e0e0e0;
    gap: 8px;
}

.category-tabs {
    display: flex;
    gap: 8px;
    flex: 1;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.category-tabs::-webkit-scrollbar {
    display: none;
}

.category-tab {
    padding: 8px 16px;
    border-radius: 20px;
    background: #f0f0f0;
    border: none;
    cursor: pointer;
    white-space: nowrap;
    font-size: 14px;
    transition: all 0.2s ease;
}

.category-tab.active {
    background: #6366f1;
    color: white;
}

.category-tab:hover {
    background: #e0e0e0;
}

.category-tab.active:hover {
    background: #5856eb;
}

.add-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: #6366f1;
    color: white;
    border: none;
    cursor: pointer;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s ease;
}

.add-btn:hover {
    background: #5856eb;
}

/* 提示词列表 */
.prompt-list {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.prompt-card {
    background: white;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    border: 1px solid transparent;
}

.prompt-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    border-color: #6366f1;
}

.prompt-info {
    flex: 1;
    cursor: pointer;
}

.prompt-name {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 6px;
    color: #333;
    line-height: 1.4;
}

.prompt-preview {
    font-size: 13px;
    color: #666;
    margin-bottom: 8px;
    line-height: 1.4;
    opacity: 0.8;
}

.prompt-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: #666;
    flex-wrap: wrap;
}

.prompt-model {
    background: #e0e7ff;
    color: #6366f1;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.prompt-category {
    background: #f3f4f6;
    color: #6b7280;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.prompt-actions {
    display: flex;
    flex-direction: column;
    gap: 6px;
    margin-left: 12px;
}

.use-btn, .edit-btn, .delete-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    min-width: 60px;
    text-align: center;
}

.use-btn {
    background: #6366f1;
    color: white;
}

.use-btn:hover {
    background: #5856eb;
}

.edit-btn {
    background: #f59e0b;
    color: white;
}

.edit-btn:hover {
    background: #d97706;
}

.delete-btn {
    background: #ef4444;
    color: white;
}

.delete-btn:hover {
    background: #dc2626;
}

/* 底部操作区 */
.bottom-actions {
    padding: 16px;
    background: white;
    border-top: 1px solid #e0e0e0;
}

.action-row {
    display: flex;
    gap: 8px;
}

.action-btn {
    flex: 1;
    padding: 10px 8px;
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.action-btn .icon {
    font-size: 16px;
}

.settings-btn {
    flex: 1;
    padding: 10px 8px;
    background: #6366f1;
    color: white;
    border: 1px solid #6366f1;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    transition: all 0.2s ease;
}

.settings-btn:hover {
    background: #5856eb;
    transform: translateY(-1px);
}

.settings-btn .icon {
    font-size: 16px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.required {
    color: #ef4444;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group textarea {
    min-height: 100px;
    resize: vertical;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary {
    background: #6366f1;
    color: white;
}

.btn-primary:hover {
    background: #5856eb;
}

.btn-secondary {
    background: #f8f9fa;
    color: #6c757d;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background: #e9ecef;
}

.btn-success {
    background: #10b981;
    color: white;
}

.btn-success:hover {
    background: #059669;
}

/* 加载指示器 */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 20px;
    color: #6366f1;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #e0e7ff;
    border-top: 2px solid #6366f1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-state .icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state h3 {
    margin-bottom: 8px;
    color: #495057;
}

/* Toast 动画 */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* 卡片进入动画 */
.prompt-card {
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 分类标签动画 */
.category-tab {
    position: relative;
    overflow: hidden;
}

.category-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.category-tab:hover::before {
    left: 100%;
}

/* 按钮点击效果 */
.btn:active, .use-btn:active, .edit-btn:active, .delete-btn:active {
    transform: scale(0.95);
}

/* 加载状态增强 */
.loading {
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #6366f1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .container {
        max-width: 100%;
    }

    .category-nav {
        padding: 12px;
    }

    .prompt-list {
        padding: 12px;
    }

    .prompt-card {
        padding: 12px;
        flex-direction: column;
        align-items: stretch;
    }

    .prompt-actions {
        flex-direction: row;
        margin-left: 0;
        margin-top: 12px;
        justify-content: space-between;
    }

    .use-btn, .edit-btn, .delete-btn {
        flex: 1;
        margin: 0 2px;
    }
}
